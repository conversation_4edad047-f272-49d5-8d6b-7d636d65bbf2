// Shining C Music App JavaScript
// Main application JavaScript file

// Mobile Menu Functions
function openMobileMenu() {
    console.log('Opening mobile menu...');
    const menu = document.getElementById('mobileMenu');
    const overlay = document.getElementById('mobileMenuOverlay');

    if (menu && overlay) {
        menu.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
        console.log('Mobile menu opened');
    } else {
        console.error('Menu or overlay not found');
    }
}

function closeMobileMenu() {
    console.log('Closing mobile menu...');
    const menu = document.getElementById('mobileMenu');
    const overlay = document.getElementById('mobileMenuOverlay');

    if (menu && overlay) {
        menu.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = ''; // Restore scrolling
        console.log('Mobile menu closed');
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, mobile menu ready');
    const menuButton = document.querySelector('.mobile-menu-btn');
    const menu = document.getElementById('mobileMenu');
    console.log('Menu button found:', !!menuButton);
    console.log('Menu element found:', !!menu);
    
    // Add keyboard support for accessibility
    document.addEventListener('keydown', function(event) {
        // Close menu when Escape key is pressed
        if (event.key === 'Escape') {
            const menu = document.getElementById('mobileMenu');
            if (menu && menu.classList.contains('show')) {
                closeMobileMenu();
            }
        }
    });
});
